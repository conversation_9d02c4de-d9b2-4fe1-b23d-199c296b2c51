import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'
import { schedulerService } from '../services/schedulerService'

export class SchedulerController {
  /**
   * 获取调度器状态
   */
  async getStatus(req: Request, res: Response): Promise<void> {
    try {
      const status = schedulerService.getStatus()
      
      res.json({
        success: true,
        data: status,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取调度器状态失败:', error)
      res.status(500).json({
        success: false,
        message: '获取调度器状态失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取所有任务
   */
  async getTasks(req: Request, res: Response): Promise<void> {
    try {
      const tasks = schedulerService.getTasks()
      
      res.json({
        success: true,
        data: tasks,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取任务列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取任务列表失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 手动执行任务
   */
  async executeTask(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { taskId } = req.params
      
      await schedulerService.executeTask(taskId)
      
      res.json({
        success: true,
        message: `任务 ${taskId} 执行成功`,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('手动执行任务失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '执行任务失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 启用/禁用任务
   */
  async toggleTask(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { taskId } = req.params
      const { isActive } = req.body
      
      schedulerService.toggleTask(taskId, isActive)
      
      res.json({
        success: true,
        message: `任务 ${taskId} 已${isActive ? '启用' : '禁用'}`,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('切换任务状态失败:', error)
      res.status(500).json({
        success: false,
        message: '切换任务状态失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 启动调度器
   */
  async startScheduler(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      schedulerService.start()
      
      res.json({
        success: true,
        message: '调度器已启动',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('启动调度器失败:', error)
      res.status(500).json({
        success: false,
        message: '启动调度器失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 停止调度器
   */
  async stopScheduler(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      schedulerService.stop()
      
      res.json({
        success: true,
        message: '调度器已停止',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('停止调度器失败:', error)
      res.status(500).json({
        success: false,
        message: '停止调度器失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const schedulerController = new SchedulerController()
