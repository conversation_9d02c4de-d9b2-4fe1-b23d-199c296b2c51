import { eq, desc, and, sql, count, sum, max } from 'drizzle-orm'
import { v4 as uuidv4 } from 'uuid'
import { db } from '../database/connection'
import { debts, debtTypes, debtPayments } from '../database/schema'
import { logger } from '../utils/logger'
import type { Debt, DebtType, DebtPayment } from '../types'

const generateId = () => uuidv4()

export class DebtService {
  /**
   * 获取所有负债
   */
  async getAllDebts(): Promise<Debt[]> {
    try {
      const allDebts = await db().select().from(debts).orderBy(desc(debts.updatedAt))

      logger.info(`获取负债成功, 数量: ${allDebts.length}`)
      return allDebts
    } catch (error) {
      logger.error('获取负债失败:', error)
      throw error
    }
  }

  /**
   * 根据类型获取负债
   */
  async getDebtsByType(typeId: string): Promise<Debt[]> {
    try {
      const typeDebts = await db()
        .select()
        .from(debts)
        .where(eq(debts.typeId, typeId))
        .orderBy(desc(debts.updatedAt))

      return typeDebts
    } catch (error) {
      logger.error('根据类型获取负债失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取单个负债
   */
  async getDebtById(id: string): Promise<Debt | null> {
    try {
      const debt = await db().select().from(debts).where(eq(debts.id, id)).limit(1)

      return debt[0] || null
    } catch (error) {
      logger.error('根据ID获取负债失败:', error)
      throw error
    }
  }

  /**
   * 创建新负债
   */
  async createDebt(
    debtData: Omit<Debt, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Debt> {
    try {
      // 验证负债类型是否存在
      const typeExists = await db()
        .select({ id: debtTypes.id })
        .from(debtTypes)
        .where(eq(debtTypes.id, debtData.typeId))
        .limit(1)

      if (!typeExists.length) {
        throw new Error(`负债类型不存在: ${debtData.typeId}`)
      }

      const newDebt = await db()
        .insert(debts)
        .values({
          id: generateId(),
          ...debtData,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning()

      if (!newDebt[0]) {
        throw new Error('负债创建失败')
      }

      logger.info(`负债创建成功: ${newDebt[0].id}`)
      return newDebt[0]
    } catch (error) {
      logger.error('创建负债失败:', error)
      throw error
    }
  }

  /**
   * 更新负债
   */
  async updateDebt(
    id: string,
    updateData: Partial<Omit<Debt, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<Debt> {
    try {
      const updatedDebt = await db()
        .update(debts)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(debts.id, id))
        .returning()

      if (!updatedDebt[0]) {
        throw new Error(`负债不存在或更新失败: ${id}`)
      }

      logger.info(`负债更新成功: ${id}`)
      return updatedDebt[0]
    } catch (error) {
      logger.error('更新负债失败:', error)
      throw error
    }
  }

  /**
   * 删除负债
   */
  async deleteDebt(id: string): Promise<void> {
    try {
      const deletedDebt = await db().delete(debts).where(eq(debts.id, id)).returning()

      if (!deletedDebt.length) {
        throw new Error(`负债不存在: ${id}`)
      }

      logger.info(`负债删除成功: ${id}`)
    } catch (error) {
      logger.error('删除负债失败:', error)
      throw error
    }
  }

  /**
   * 获取负债统计信息
   */
  async getDebtStatistics(): Promise<{
    totalDebts: number
    totalPrincipal: number
    totalCurrentBalance: number
    averageInterestRate: number
    totalMonthlyPayment: number
    debtsByType: Array<{
      typeId: string
      typeName: string
      count: number
      totalBalance: number
    }>
  }> {
    try {
      // 获取总体统计
      const totalStats = await db()
        .select({
          count: count(),
          totalPrincipal: sum(debts.principalAmount),
          totalCurrentBalance: sum(debts.currentBalance),
          avgInterestRate: sql<number>`AVG(${debts.interestRate})`,
          totalMonthlyPayment: sum(debts.monthlyPayment),
        })
        .from(debts)

      // 按类型统计
      const typeStats = await db()
        .select({
          typeId: debts.typeId,
          typeName: debtTypes.name,
          count: count(),
          totalBalance: sum(debts.currentBalance),
        })
        .from(debts)
        .leftJoin(debtTypes, eq(debts.typeId, debtTypes.id))
        .groupBy(debts.typeId, debtTypes.name)

      const stats = totalStats[0]
      return {
        totalDebts: stats?.count || 0,
        totalPrincipal: Number(stats?.totalPrincipal) || 0,
        totalCurrentBalance: Number(stats?.totalCurrentBalance) || 0,
        averageInterestRate: stats?.avgInterestRate || 0,
        totalMonthlyPayment: Number(stats?.totalMonthlyPayment) || 0,
        debtsByType: typeStats.map((stat) => ({
          typeId: stat.typeId,
          typeName: stat.typeName || '',
          count: stat.count,
          totalBalance: Number(stat.totalBalance) || 0,
        })),
      }
    } catch (error) {
      logger.error('获取负债统计失败:', error)
      throw error
    }
  }

  /**
   * 获取所有负债类型
   */
  async getAllDebtTypes(): Promise<DebtType[]> {
    try {
      const types = await db().select().from(debtTypes).orderBy(debtTypes.name)
      return types
    } catch (error) {
      logger.error('获取负债类型失败:', error)
      throw error
    }
  }

  /**
   * 计算负债健康评分
   */
  calculateDebtHealthScore(debt: Debt): number {
    try {
      let score = 100

      // 债务利用率 (当前余额 / 原始本金)
      const utilizationRate = debt.currentBalance / debt.principalAmount
      if (utilizationRate > 0.8) score -= 30
      else if (utilizationRate > 0.6) score -= 20
      else if (utilizationRate > 0.4) score -= 10

      // 利率水平
      if (debt.interestRate > 15) score -= 25
      else if (debt.interestRate > 10) score -= 15
      else if (debt.interestRate > 5) score -= 5

      // 月供负担 (如果有月供信息)
      if (debt.monthlyPayment) {
        const paymentToBalance = debt.monthlyPayment / debt.currentBalance
        if (paymentToBalance < 0.02) score -= 15 // 月供过低，还款周期过长
        else if (paymentToBalance > 0.1) score += 10 // 月供较高，还款积极
      }

      return Math.max(0, Math.min(100, score))
    } catch (error) {
      logger.error('计算负债健康评分失败:', error)
      return 50 // 默认中等评分
    }
  }

  /**
   * 添加还款记录
   */
  async addPayment(
    debtId: string,
    paymentData: Omit<DebtPayment, 'id' | 'debtId' | 'createdAt'>
  ): Promise<DebtPayment> {
    try {
      // 验证负债是否存在
      const existingDebt = await this.getDebtById(debtId)
      if (!existingDebt) {
        throw new Error(`负债不存在: ${debtId}`)
      }

      const newPayment = await db()
        .insert(debtPayments)
        .values({
          id: generateId(),
          debtId,
          ...paymentData,
          createdAt: new Date(),
        })
        .returning()

      if (!newPayment[0]) {
        throw new Error('还款记录创建失败')
      }

      // 更新负债余额
      const newBalance = existingDebt.currentBalance - paymentData.principalAmount
      await this.updateDebt(debtId, { currentBalance: Math.max(0, newBalance) })

      logger.info(`还款记录创建成功: ${newPayment[0].id}`)
      return newPayment[0]
    } catch (error: any) {
      logger.error('添加还款记录失败:', error)
      throw error
    }
  }

  /**
   * 获取负债的还款记录
   */
  async getDebtPayments(debtId: string): Promise<DebtPayment[]> {
    try {
      const payments = await db()
        .select()
        .from(debtPayments)
        .where(eq(debtPayments.debtId, debtId))
        .orderBy(desc(debtPayments.createdAt))

      return payments
    } catch (error) {
      logger.error('获取还款记录失败:', error)
      throw error
    }
  }
}

export const debtService = new DebtService()
