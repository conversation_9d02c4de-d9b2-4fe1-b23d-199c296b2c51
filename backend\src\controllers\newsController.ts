import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'
import { newsService } from '../services/newsService'
import { db } from '../database/connection'
import { news } from '../database/schema'
import { eq, desc, like, and, gte, asc, or, count } from 'drizzle-orm'

export class NewsController {
  /**
   * 获取新闻列表（自动检查缓存并拉取最新数据）
   */
  async getNews(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // 检查是否需要刷新新闻数据
      const updateInfo = newsService.getLastUpdateInfo()
      if (updateInfo.needsRefresh) {
        logger.info('检测到需要刷新新闻数据，开始拉取...')
        try {
          await newsService.fetchAllNews()
        } catch (fetchError) {
          logger.error('自动拉取新闻失败:', fetchError)
          // 继续返回现有数据，不阻塞用户请求
        }
      }

      const {
        page = 1,
        limit = 20,
        source,
        search,
        minRelevance,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = req.query

      const pageNum = parseInt(page as string, 10)
      const limitNum = parseInt(limit as string, 10)
      const offset = (pageNum - 1) * limitNum

      // 构建查询条件
      const conditions = []

      if (source) {
        conditions.push(eq(news.source, source as string))
      }

      if (search) {
        conditions.push(like(news.title, `%${search}%`))
      }

      if (minRelevance) {
        conditions.push(gte(news.relevanceScore, parseFloat(minRelevance as string)))
      }

      // 构建排序
      const sortColumn = news[sortBy as keyof typeof news] || news.createdAt
      const orderBy = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn)

      // 构建查询
      let query = db().select().from(news)
      let countQuery = db().select({ count: count() }).from(news)

      if (conditions.length > 0) {
        query = query.where(and(...conditions))
        countQuery = countQuery.where(and(...conditions))
      }

      // 排序和分页
      query = query.orderBy(orderBy).limit(limitNum).offset(offset)

      const newsItems = await query
      const [{ count: total }] = await countQuery

      // 格式化返回数据
      const formattedNews = newsItems.map((item) => ({
        id: item.id,
        title: item.title,
        content: item.content,
        summary: item.summary,
        url: item.url,
        source: item.source,
        author: item.author,
        publishedDate: item.publishedDate,
        relevanceScore: item.relevanceScore,
        categories: item.categories ? JSON.parse(item.categories) : null,
        metadata: item.metadata ? JSON.parse(item.metadata) : null,
        isRead: item.isRead,
        isBookmarked: item.isBookmarked,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }))

      res.json({
        success: true,
        data: formattedNews,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
          hasNext: offset + limitNum < total,
          hasPrev: pageNum > 1,
        },
        lastUpdate: updateInfo.lastUpdate,
        nextRefreshIn: updateInfo.nextRefreshIn,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻失败:', error)
      res.status(500).json({
        success: false,
        message: '获取新闻失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取单条新闻详情
   */
  async getNewsById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { newsId } = req.params

      // 从数据库查询新闻详情
      const newsItem = await db.select().from(news).where(eq(news.id, newsId)).limit(1)

      if (newsItem.length === 0) {
        res.status(404).json({
          success: false,
          message: '新闻不存在',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const item = newsItem[0]
      const formattedNews = {
        id: item.id,
        title: item.title,
        content: item.content,
        summary: item.summary,
        url: item.url,
        source: item.source,
        author: item.author,
        publishedDate: item.publishedDate,
        relevanceScore: item.relevanceScore,
        categories: item.categories ? JSON.parse(item.categories) : null,
        metadata: item.metadata ? JSON.parse(item.metadata) : null,
        isRead: item.isRead,
        isBookmarked: item.isBookmarked,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }

      res.json({
        success: true,
        data: formattedNews,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取新闻详情失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取新闻分类
   */
  async getNewsCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = [
        { id: 'market', name: '市场动态', description: '股市、债市等金融市场新闻' },
        { id: 'policy', name: '政策解读', description: '货币政策、财政政策等' },
        { id: 'company', name: '公司新闻', description: '上市公司动态' },
        { id: 'economy', name: '宏观经济', description: '经济数据、经济分析' },
        { id: 'international', name: '国际财经', description: '国际市场动态' },
      ]

      res.json({
        success: true,
        data: categories,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻分类失败:', error)
      res.status(500).json({
        success: false,
        message: '获取新闻分类失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 手动拉取新闻
   */
  async fetchNews(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      logger.info('开始手动拉取新闻')
      const result = await newsService.fetchAllNews()

      res.json({
        success: true,
        data: result,
        message: `成功拉取 ${result.total} 条新闻，保存 ${result.saved} 条`,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('手动拉取新闻失败:', error)
      res.status(500).json({
        success: false,
        message: '拉取新闻失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取新闻服务状态
   */
  async getNewsServiceStatus(req: Request, res: Response): Promise<void> {
    try {
      const status = newsService.getStatus()

      res.json({
        success: true,
        data: status,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻服务状态失败:', error)
      res.status(500).json({
        success: false,
        message: '获取服务状态失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 标记新闻为已读
   */
  async markNewsAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { newsId } = req.params

      await db
        .update(news)
        .set({
          isRead: true,
          updatedAt: new Date(),
        })
        .where(eq(news.id, newsId))

      res.json({
        success: true,
        message: '新闻已标记为已读',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('标记新闻为已读失败:', error)
      res.status(500).json({
        success: false,
        message: '标记失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 收藏/取消收藏新闻
   */
  async toggleBookmark(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { newsId } = req.params
      const { bookmarked } = req.body

      await db
        .update(news)
        .set({
          isBookmarked: bookmarked,
          updatedAt: new Date(),
        })
        .where(eq(news.id, newsId))

      res.json({
        success: true,
        message: bookmarked ? '新闻已收藏' : '已取消收藏',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('切换收藏状态失败:', error)
      res.status(500).json({
        success: false,
        message: '操作失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取收藏的新闻
   */
  async getBookmarkedNews(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20 } = req.query
      const pageNum = parseInt(page as string, 10)
      const limitNum = parseInt(limit as string, 10)
      const offset = (pageNum - 1) * limitNum

      const bookmarkedNews = await db
        .select()
        .from(news)
        .where(eq(news.isBookmarked, true))
        .orderBy(desc(news.updatedAt))
        .limit(limitNum)
        .offset(offset)

      const total = await db
        .select({ count: news.id })
        .from(news)
        .where(eq(news.isBookmarked, true))

      const formattedNews = bookmarkedNews.map((item) => ({
        id: item.id,
        title: item.title,
        content: item.content,
        summary: item.summary,
        url: item.url,
        source: item.source,
        author: item.author,
        publishedDate: item.publishedDate,
        relevanceScore: item.relevanceScore,
        categories: item.categories ? JSON.parse(item.categories) : null,
        metadata: item.metadata ? JSON.parse(item.metadata) : null,
        isRead: item.isRead,
        isBookmarked: item.isBookmarked,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }))

      res.json({
        success: true,
        data: formattedNews,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: total.length,
          totalPages: Math.ceil(total.length / limitNum),
          hasNext: offset + limitNum < total.length,
          hasPrev: pageNum > 1,
        },
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取收藏新闻失败:', error)
      res.status(500).json({
        success: false,
        message: '获取收藏新闻失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const newsController = new NewsController()
