import { Router } from 'express'
import { schedulerController } from '../controllers/schedulerController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 应用认证中间件（单用户模式）
router.use(authMiddleware)

// 获取调度器状态（无需认证）
router.get('/status', asyncHandler(schedulerController.getStatus))

// 获取所有任务
router.get('/tasks', asyncHandler(schedulerController.getTasks))

// 手动执行任务
router.post('/tasks/:taskId/execute', asyncHandler(schedulerController.executeTask))

// 启用/禁用任务
router.patch('/tasks/:taskId/toggle', asyncHandler(schedulerController.toggleTask))

// 启动调度器
router.post('/start', asyncHandler(schedulerController.startScheduler))

// 停止调度器
router.post('/stop', async<PERSON>and<PERSON>(schedulerController.stopScheduler))

export default router
