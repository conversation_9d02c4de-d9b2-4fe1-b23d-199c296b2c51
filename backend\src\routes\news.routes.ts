import { Router } from 'express'
import { newsController } from '../controllers/newsController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 应用认证中间件（单用户模式）
router.use(authMiddleware)

// 获取新闻分类（无需认证）
router.get('/categories', asyncHandler(newsController.getNewsCategories))

// 获取新闻服务状态
router.get('/service/status', asyncHandler(newsController.getNewsServiceStatus))

// 手动拉取新闻
router.post('/fetch', asyncHandler(newsController.fetchNews))

// 获取收藏的新闻
router.get('/bookmarked', asyncHandler(newsController.getBookmarkedNews))

// 获取新闻列表
router.get('/', asyncHandler(newsController.getNews))

// 获取单条新闻详情
router.get('/:newsId', asyncHandler(newsController.getNewsById))

// 标记新闻为已读
router.patch('/:newsId/read', asyncHandler(newsController.markNewsAsRead))

// 收藏/取消收藏新闻
router.patch('/:newsId/bookmark', asyncHandler(newsController.toggleBookmark))

export default router
