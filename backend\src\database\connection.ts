import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import path from 'path'
import fs from 'fs'
import { logger } from '../utils/logger'
import * as schema from './schema'

let db: Database.Database
let drizzleDb: ReturnType<typeof drizzle>

export async function initializeDatabase(): Promise<void> {
  try {
    const dbPath = process.env.DATABASE_PATH || '../data/database.sqlite'
    const fullPath = path.resolve(dbPath)

    // 确保数据目录存在
    const dataDir = path.dirname(fullPath)
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
      logger.info(`创建数据目录: ${dataDir}`)
    }

    // 创建数据库连接
    db = new Database(fullPath, {
      verbose:
        process.env.NODE_ENV === 'development'
          ? (message?: unknown) => logger.debug(String(message))
          : undefined,
    })

    // 性能优化配置
    db.pragma('journal_mode = WAL')
    db.pragma('synchronous = NORMAL')
    db.pragma('cache_size = 1000000')
    db.pragma('temp_store = MEMORY')
    db.pragma('mmap_size = 268435456') // 256MB

    // 创建Drizzle实例
    drizzleDb = drizzle(db, { schema })

    // 运行数据库迁移
    await runMigrations()

    logger.info(`数据库连接成功: ${fullPath}`)
  } catch (error) {
    logger.error('数据库初始化失败:', error)
    throw error
  }
}

async function runMigrations(): Promise<void> {
  try {
    // 创建应用设置表（单用户模式）
    db.exec(`
      CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        ai_api_keys TEXT,
        preferred_currency TEXT DEFAULT 'USD',
        timezone TEXT DEFAULT 'UTC',
        dashboard_layout TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建资产分类表
    db.exec(`
      CREATE TABLE IF NOT EXISTS asset_categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        color TEXT
      )
    `)

    // 创建资产表
    db.exec(`
      CREATE TABLE IF NOT EXISTS assets (
        id TEXT PRIMARY KEY,
        category_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        current_value DECIMAL(15,2) NOT NULL,
        purchase_price DECIMAL(15,2),
        purchase_date DATE,
        currency TEXT DEFAULT 'USD',
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES asset_categories(id)
      )
    `)

    // 创建资产历史表
    db.exec(`
      CREATE TABLE IF NOT EXISTS asset_history (
        id TEXT PRIMARY KEY,
        asset_id TEXT NOT NULL,
        value DECIMAL(15,2) NOT NULL,
        record_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE
      )
    `)

    // 创建负债类型表
    db.exec(`
      CREATE TABLE IF NOT EXISTS debt_types (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        color TEXT
      )
    `)

    // 创建负债表
    db.exec(`
      CREATE TABLE IF NOT EXISTS debts (
        id TEXT PRIMARY KEY,
        type_id TEXT NOT NULL,
        name TEXT NOT NULL,
        principal_amount DECIMAL(15,2) NOT NULL,
        current_balance DECIMAL(15,2) NOT NULL,
        interest_rate DECIMAL(5,4) NOT NULL,
        start_date DATE NOT NULL,
        monthly_payment DECIMAL(15,2),
        currency TEXT DEFAULT 'USD',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (type_id) REFERENCES debt_types(id)
      )
    `)

    // 创建还款记录表
    db.exec(`
      CREATE TABLE IF NOT EXISTS debt_payments (
        id TEXT PRIMARY KEY,
        debt_id TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        principal_amount DECIMAL(15,2) NOT NULL,
        interest_amount DECIMAL(15,2) NOT NULL,
        payment_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (debt_id) REFERENCES debts(id) ON DELETE CASCADE
      )
    `)

    // 创建AI模型表
    db.exec(`
      CREATE TABLE IF NOT EXISTS ai_models (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        provider TEXT NOT NULL,
        model_id TEXT NOT NULL,
        cost_per_token DECIMAL(10,8),
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建AI对话表
    db.exec(`
      CREATE TABLE IF NOT EXISTS ai_conversations (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        context_type TEXT,
        context_data TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建AI消息表
    db.exec(`
      CREATE TABLE IF NOT EXISTS ai_messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        model_id TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        metadata TEXT,
        tokens_used INTEGER,
        cost DECIMAL(10,8),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE,
        FOREIGN KEY (model_id) REFERENCES ai_models(id)
      )
    `)

    // 创建新闻表
    db.exec(`
      CREATE TABLE IF NOT EXISTS news (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT,
        summary TEXT,
        url TEXT,
        source TEXT,
        author TEXT,
        published_date DATE,
        relevance_score DECIMAL(3,2),
        categories TEXT,
        metadata TEXT,
        is_read BOOLEAN DEFAULT FALSE,
        is_bookmarked BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建新闻表索引
    db.exec(`CREATE INDEX IF NOT EXISTS idx_news_title ON news(title)`)
    db.exec(`CREATE INDEX IF NOT EXISTS idx_news_source ON news(source)`)
    db.exec(
      `CREATE INDEX IF NOT EXISTS idx_news_published_date ON news(published_date)`
    )
    db.exec(
      `CREATE INDEX IF NOT EXISTS idx_news_relevance_score ON news(relevance_score)`
    )
    db.exec(`CREATE INDEX IF NOT EXISTS idx_news_created_at ON news(created_at)`)
    db.exec(`CREATE INDEX IF NOT EXISTS idx_news_is_read ON news(is_read)`)
    db.exec(`CREATE INDEX IF NOT EXISTS idx_news_is_bookmarked ON news(is_bookmarked)`)

    // 创建市场指标表
    db.exec(`
      CREATE TABLE IF NOT EXISTS market_indicators (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        description TEXT,
        category TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建市场数据表
    db.exec(`
      CREATE TABLE IF NOT EXISTS market_data (
        id TEXT PRIMARY KEY,
        indicator_id TEXT NOT NULL,
        value DECIMAL(15,4) NOT NULL,
        change_amount DECIMAL(15,4),
        change_percentage DECIMAL(5,2),
        record_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (indicator_id) REFERENCES market_indicators(id)
      )
    `)

    // 创建日历事件表
    db.exec(`
      CREATE TABLE IF NOT EXISTS calendar_events (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        event_type TEXT,
        start_time DATETIME NOT NULL,
        end_time DATETIME,
        is_recurring BOOLEAN DEFAULT FALSE,
        recurrence_rule TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建索引
    createIndexes()

    // 插入初始数据
    await seedInitialData()

    logger.info('数据库迁移完成')
  } catch (error) {
    console.error(error)
    logger.error('数据库迁移失败:', error)
    throw error
  }
}

function createIndexes(): void {
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
    'CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_assets_user_id ON assets(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_assets_category_id ON assets(category_id)',
    'CREATE INDEX IF NOT EXISTS idx_asset_history_asset_id ON asset_history(asset_id)',
    'CREATE INDEX IF NOT EXISTS idx_asset_history_record_date ON asset_history(record_date)',
    'CREATE INDEX IF NOT EXISTS idx_debts_user_id ON debts(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_debt_payments_debt_id ON debt_payments(debt_id)',
    'CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_id ON ai_conversations(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_ai_messages_conversation_id ON ai_messages(conversation_id)',
    'CREATE INDEX IF NOT EXISTS idx_news_published_date ON news(published_date)',
    'CREATE INDEX IF NOT EXISTS idx_news_relevance_score ON news(relevance_score)',
    'CREATE INDEX IF NOT EXISTS idx_market_data_indicator_id ON market_data(indicator_id)',
    'CREATE INDEX IF NOT EXISTS idx_market_data_record_date ON market_data(record_date)',
    'CREATE INDEX IF NOT EXISTS idx_calendar_events_user_id ON calendar_events(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_calendar_events_start_time ON calendar_events(start_time)',
    'CREATE UNIQUE INDEX IF NOT EXISTS idx_market_data_unique ON market_data(indicator_id, record_date)',
  ]

  indexes.forEach((indexSql) => {
    try {
      db.exec(indexSql)
    } catch (error) {
      logger.warn('创建索引失败:', indexSql, error)
    }
  })
}

async function seedInitialData(): Promise<void> {
  try {
    // 插入资产分类
    const assetCategories = [
      {
        id: 'cash',
        name: '现金',
        description: '现金和现金等价物',
        icon: 'cash',
        color: '#4CAF50',
      },
      {
        id: 'stocks',
        name: '股票',
        description: '股票投资',
        icon: 'trending-up',
        color: '#2196F3',
      },
      {
        id: 'bonds',
        name: '债券',
        description: '债券投资',
        icon: 'shield',
        color: '#FF9800',
      },
      {
        id: 'real_estate',
        name: '房产',
        description: '房地产投资',
        icon: 'home',
        color: '#9C27B0',
      },
      {
        id: 'vehicles',
        name: '车辆',
        description: '汽车等交通工具',
        icon: 'car',
        color: '#607D8B',
      },
      {
        id: 'crypto',
        name: '加密货币',
        description: '数字货币投资',
        icon: 'bitcoin',
        color: '#FFC107',
      },
    ]

    const insertAssetCategory = db.prepare(`
      INSERT OR IGNORE INTO asset_categories (id, name, description, icon, color)
      VALUES (?, ?, ?, ?, ?)
    `)

    assetCategories.forEach((category) => {
      insertAssetCategory.run(
        category.id,
        category.name,
        category.description,
        category.icon,
        category.color
      )
    })

    // 插入负债类型
    const debtTypes = [
      {
        id: 'credit_card',
        name: '信用卡',
        description: '信用卡债务',
        icon: 'credit-card',
        color: '#F44336',
      },
      {
        id: 'mortgage',
        name: '房贷',
        description: '房屋抵押贷款',
        icon: 'home',
        color: '#3F51B5',
      },
      {
        id: 'auto_loan',
        name: '车贷',
        description: '汽车贷款',
        icon: 'car',
        color: '#009688',
      },
      {
        id: 'personal_loan',
        name: '个人贷款',
        description: '个人消费贷款',
        icon: 'user',
        color: '#795548',
      },
      {
        id: 'student_loan',
        name: '学生贷款',
        description: '教育贷款',
        icon: 'book',
        color: '#E91E63',
      },
    ]

    const insertDebtType = db.prepare(`
      INSERT OR IGNORE INTO debt_types (id, name, description, icon, color)
      VALUES (?, ?, ?, ?, ?)
    `)

    debtTypes.forEach((type) => {
      insertDebtType.run(type.id, type.name, type.description, type.icon, type.color)
    })

    // 插入AI模型
    const aiModels = [
      {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        provider: 'deepseek',
        model_id: 'deepseek-chat',
        cost_per_token: 0.00000014,
        is_active: true,
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'openai',
        model_id: 'gpt-4',
        cost_per_token: 0.00003,
        is_active: true,
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        provider: 'openai',
        model_id: 'gpt-4o-mini',
        cost_per_token: 0.00000015,
        is_active: true,
      },
      {
        id: 'claude-3-sonnet',
        name: 'Claude 3 Sonnet',
        provider: 'anthropic',
        model_id: 'claude-3-sonnet-20240229',
        cost_per_token: 0.000015,
        is_active: true,
      },
    ]

    const insertAIModel = db.prepare(`
      INSERT OR IGNORE INTO ai_models (id, name, provider, model_id, cost_per_token, is_active, created_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `)

    aiModels.forEach((model) => {
      insertAIModel.run(
        model.id,
        model.name,
        model.provider,
        model.model_id,
        model.cost_per_token,
        model.is_active ? 1 : 0
      )
    })

    // 插入市场指标
    const marketIndicators = [
      {
        id: 'sp500',
        name: 'S&P 500',
        symbol: 'SPX',
        description: '标普500指数',
        category: 'index',
        is_active: true,
      },
      {
        id: 'nasdaq',
        name: 'NASDAQ',
        symbol: 'IXIC',
        description: '纳斯达克综合指数',
        category: 'index',
        is_active: true,
      },
      {
        id: 'gold',
        name: '黄金',
        symbol: 'GOLD',
        description: '黄金现货价格',
        category: 'commodity',
        is_active: true,
      },
      {
        id: 'usd_cny',
        name: '美元人民币',
        symbol: 'USDCNY',
        description: '美元兑人民币汇率',
        category: 'currency',
        is_active: true,
      },
    ]

    const insertMarketIndicator = db.prepare(`
      INSERT OR IGNORE INTO market_indicators (id, name, symbol, description, category, is_active, created_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `)

    marketIndicators.forEach((indicator) => {
      insertMarketIndicator.run(
        indicator.id,
        indicator.name,
        indicator.symbol,
        indicator.description,
        indicator.category,
        indicator.is_active ? 1 : 0
      )
    })

    logger.info('初始数据插入完成')
  } catch (error) {
    logger.error('初始数据插入失败:', error)
    throw error
  }
}

export function getDatabase(): Database.Database {
  if (!db) {
    throw new Error('数据库未初始化')
  }
  return db
}

export function getDrizzleDb() {
  if (!drizzleDb) {
    throw new Error('Drizzle数据库未初始化')
  }
  return drizzleDb
}

// 导出db实例供其他模块使用
export { getDrizzleDb as db }

export function closeDatabase(): void {
  if (db) {
    db.close()
    logger.info('数据库连接已关闭')
  }
}
