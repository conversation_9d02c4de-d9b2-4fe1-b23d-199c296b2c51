import { logger } from '../utils/logger'
import { db } from '../database/connection'
import { news } from '../database/schema'
import { eq, desc, and, gte } from 'drizzle-orm'
import { v4 as uuidv4 } from 'uuid'

export interface NewsSource {
  id: string
  name: string
  category: string
}

export interface NewsItem {
  id: string
  title: string
  content?: string
  summary?: string
  url?: string
  source: string
  publishedDate: string
  relevanceScore?: number
  createdAt: Date
}

export interface NewsServiceConfig {
  sources: NewsSource[]
  maxNewsPerSource: number
  updateInterval: number // 分钟
}

export class NewsService {
  private config: NewsServiceConfig
  private isRunning: boolean = false

  // 财经相关的新闻源配置
  private readonly FINANCIAL_SOURCES: NewsSource[] = [
    {
      id: 'wallstreetcn-hot',
      name: '华尔街见闻-最热文章',
      category: 'finance',
    },
    {
      id: 'wallstreetcn-news',
      name: '华尔街见闻-最新资讯',
      category: 'finance',
    },
    {
      id: 'cls-hot',
      name: '财联社-热门',
      category: 'finance',
    },
    {
      id: 'cls-telegraph',
      name: '财联社-电报',
      category: 'finance',
    },
    {
      id: 'jin10',
      name: '金十数据',
      category: 'finance',
    },
  ]

  constructor(config?: Partial<NewsServiceConfig>) {
    this.config = {
      sources: this.FINANCIAL_SOURCES,
      maxNewsPerSource: 10,
      updateInterval: 30, // 30分钟更新一次
      ...config,
    }
  }

  /**
   * 从指定新闻源拉取新闻
   */
  async fetchNewsFromSource(sourceId: string, count: number = 10): Promise<any[]> {
    try {
      logger.info(`开始从 ${sourceId} 拉取新闻，数量: ${count}`)

      // 这里需要调用newsnow MCP的API
      // 由于我们在Node.js环境中，需要通过HTTP请求调用
      const response = await fetch(
        `https://newsnow.busiyi.world/api/s?id=${sourceId}&count=${count}`
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.text()

      // 解析返回的数据，通常是HTML格式的链接列表
      const newsItems = this.parseNewsData(data, sourceId)

      logger.info(`从 ${sourceId} 成功拉取 ${newsItems.length} 条新闻`)
      return newsItems
    } catch (error) {
      logger.error(`从 ${sourceId} 拉取新闻失败:`, error)
      return []
    }
  }

  /**
   * 解析新闻数据
   */
  private parseNewsData(htmlData: string, sourceId: string): any[] {
    const newsItems: any[] = []

    try {
      // 使用正则表达式解析HTML中的链接
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g
      let match

      while ((match = linkRegex.exec(htmlData)) !== null) {
        const title = match[1]
        const url = match[2]

        if (title && url) {
          newsItems.push({
            title: title.trim(),
            url: url.trim(),
            source: this.getSourceName(sourceId),
            publishedDate: new Date().toISOString().split('T')[0],
            relevanceScore: this.calculateRelevanceScore(title),
          })
        }
      }
    } catch (error) {
      logger.error('解析新闻数据失败:', error)
    }

    return newsItems
  }

  /**
   * 获取新闻源名称
   */
  private getSourceName(sourceId: string): string {
    const source = this.FINANCIAL_SOURCES.find((s) => s.id === sourceId)
    return source ? source.name : sourceId
  }

  /**
   * 计算新闻相关性评分
   */
  private calculateRelevanceScore(title: string): number {
    // 财经关键词权重
    const financialKeywords = [
      '股市',
      '股票',
      '基金',
      '债券',
      '期货',
      '外汇',
      '黄金',
      '银行',
      '保险',
      '证券',
      '投资',
      '理财',
      '经济',
      '金融',
      '央行',
      '利率',
      '通胀',
      'GDP',
      'CPI',
      'A股',
      '港股',
      '美股',
      '创业板',
      '科创板',
      '新三板',
      'IPO',
      '并购',
      '财报',
      '业绩',
      '分红',
      '增发',
      '回购',
      '重组',
    ]

    let score = 0.5 // 基础分数

    for (const keyword of financialKeywords) {
      if (title.includes(keyword)) {
        score += 0.1
      }
    }

    return Math.min(score, 1.0) // 最高1.0分
  }

  /**
   * 保存新闻到数据库
   */
  async saveNews(newsItem: any): Promise<boolean> {
    try {
      // 检查是否已存在相同标题的新闻
      const existingNews = await db
        .select()
        .from(news)
        .where(eq(news.title, newsItem.title))
        .limit(1)

      if (existingNews.length > 0) {
        logger.debug(`新闻已存在，跳过: ${newsItem.title}`)
        return false
      }

      // 插入新新闻
      const now = new Date()
      await db.insert(news).values({
        id: uuidv4(),
        title: newsItem.title,
        content: newsItem.content || null,
        summary: newsItem.summary || null,
        url: newsItem.url || null,
        source: newsItem.source,
        author: newsItem.author || null,
        publishedDate: newsItem.publishedDate,
        relevanceScore: newsItem.relevanceScore || 0.5,
        categories: newsItem.categories ? JSON.stringify(newsItem.categories) : null,
        metadata: newsItem.metadata ? JSON.stringify(newsItem.metadata) : null,
        isRead: false,
        isBookmarked: false,
        createdAt: now,
        updatedAt: now,
      })

      logger.debug(`新闻保存成功: ${newsItem.title}`)
      return true
    } catch (error) {
      logger.error('保存新闻失败:', error)
      return false
    }
  }

  /**
   * 批量拉取所有配置的新闻源
   */
  async fetchAllNews(): Promise<{ total: number; saved: number; errors: number }> {
    logger.info('开始批量拉取新闻')

    let totalFetched = 0
    let totalSaved = 0
    let totalErrors = 0

    for (const source of this.config.sources) {
      try {
        const newsItems = await this.fetchNewsFromSource(
          source.id,
          this.config.maxNewsPerSource
        )
        totalFetched += newsItems.length

        for (const item of newsItems) {
          const saved = await this.saveNews(item)
          if (saved) {
            totalSaved++
          }
        }

        // 避免请求过于频繁
        await new Promise((resolve) => setTimeout(resolve, 1000))
      } catch (error) {
        logger.error(`处理新闻源 ${source.id} 时出错:`, error)
        totalErrors++
      }
    }

    logger.info(
      `新闻拉取完成 - 总计: ${totalFetched}, 保存: ${totalSaved}, 错误: ${totalErrors}`
    )

    return {
      total: totalFetched,
      saved: totalSaved,
      errors: totalErrors,
    }
  }

  /**
   * 获取最新新闻
   */
  async getLatestNews(limit: number = 20): Promise<NewsItem[]> {
    try {
      const result = await db
        .select()
        .from(news)
        .orderBy(desc(news.createdAt))
        .limit(limit)

      return result.map((item) => ({
        id: item.id,
        title: item.title,
        content: item.content || undefined,
        summary: item.summary || undefined,
        url: item.url || undefined,
        source: item.source || '',
        publishedDate: item.publishedDate || '',
        relevanceScore: item.relevanceScore || undefined,
        createdAt: item.createdAt,
      }))
    } catch (error) {
      logger.error('获取最新新闻失败:', error)
      return []
    }
  }

  /**
   * 根据来源获取新闻
   */
  async getNewsBySource(source: string, limit: number = 20): Promise<NewsItem[]> {
    try {
      const result = await db
        .select()
        .from(news)
        .where(eq(news.source, source))
        .orderBy(desc(news.createdAt))
        .limit(limit)

      return result.map((item) => ({
        id: item.id,
        title: item.title,
        content: item.content || undefined,
        summary: item.summary || undefined,
        url: item.url || undefined,
        source: item.source || '',
        publishedDate: item.publishedDate || '',
        relevanceScore: item.relevanceScore || undefined,
        createdAt: item.createdAt,
      }))
    } catch (error) {
      logger.error('根据来源获取新闻失败:', error)
      return []
    }
  }

  /**
   * 清理旧新闻（保留最近7天的新闻）
   */
  async cleanupOldNews(): Promise<number> {
    try {
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const result = await db.delete(news).where(gte(news.createdAt, sevenDaysAgo))

      logger.info(`清理了 ${result.changes} 条旧新闻`)
      return result.changes || 0
    } catch (error) {
      logger.error('清理旧新闻失败:', error)
      return 0
    }
  }

  /**
   * 启动定时新闻拉取
   */
  startScheduledFetch(): void {
    if (this.isRunning) {
      logger.warn('新闻拉取服务已在运行')
      return
    }

    this.isRunning = true
    logger.info(`启动定时新闻拉取，间隔: ${this.config.updateInterval} 分钟`)

    // 立即执行一次
    this.fetchAllNews()

    // 设置定时器
    setInterval(async () => {
      if (this.isRunning) {
        await this.fetchAllNews()
        // 每天清理一次旧新闻
        if (new Date().getHours() === 2) {
          // 凌晨2点清理
          await this.cleanupOldNews()
        }
      }
    }, this.config.updateInterval * 60 * 1000)
  }

  /**
   * 停止定时新闻拉取
   */
  stopScheduledFetch(): void {
    this.isRunning = false
    logger.info('停止定时新闻拉取')
  }

  /**
   * 获取服务状态
   */
  getStatus(): { isRunning: boolean; config: NewsServiceConfig } {
    return {
      isRunning: this.isRunning,
      config: this.config,
    }
  }
}

// 创建单例实例
export const newsService = new NewsService()
