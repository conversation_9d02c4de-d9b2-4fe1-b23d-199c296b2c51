import { logger } from '../utils/logger'
import { newsService } from './newsService'

export interface ScheduledTask {
  id: string
  name: string
  cronExpression: string
  handler: () => Promise<void>
  isActive: boolean
  lastRun?: Date
  nextRun?: Date
}

export class SchedulerService {
  private tasks: Map<string, ScheduledTask> = new Map()
  private intervals: Map<string, NodeJS.Timeout> = new Map()
  private isRunning: boolean = false

  constructor() {
    this.initializeTasks()
  }

  /**
   * 初始化默认任务
   */
  private initializeTasks(): void {
    // 新闻拉取任务 - 每30分钟执行一次
    this.addTask({
      id: 'news-fetch',
      name: '新闻拉取任务',
      cronExpression: '*/30 * * * *', // 每30分钟
      handler: this.handleNewsFetch.bind(this),
      isActive: true
    })

    // 数据清理任务 - 每天凌晨2点执行
    this.addTask({
      id: 'data-cleanup',
      name: '数据清理任务',
      cronExpression: '0 2 * * *', // 每天凌晨2点
      handler: this.handleDataCleanup.bind(this),
      isActive: true
    })
  }

  /**
   * 添加定时任务
   */
  addTask(task: Omit<ScheduledTask, 'lastRun' | 'nextRun'>): void {
    const fullTask: ScheduledTask = {
      ...task,
      nextRun: this.calculateNextRun(task.cronExpression)
    }
    
    this.tasks.set(task.id, fullTask)
    
    if (this.isRunning && task.isActive) {
      this.scheduleTask(fullTask)
    }
    
    logger.info(`添加定时任务: ${task.name} (${task.cronExpression})`)
  }

  /**
   * 移除定时任务
   */
  removeTask(taskId: string): void {
    const interval = this.intervals.get(taskId)
    if (interval) {
      clearInterval(interval)
      this.intervals.delete(taskId)
    }
    
    this.tasks.delete(taskId)
    logger.info(`移除定时任务: ${taskId}`)
  }

  /**
   * 启用/禁用任务
   */
  toggleTask(taskId: string, isActive: boolean): void {
    const task = this.tasks.get(taskId)
    if (!task) {
      logger.warn(`任务不存在: ${taskId}`)
      return
    }

    task.isActive = isActive
    
    if (isActive && this.isRunning) {
      this.scheduleTask(task)
    } else {
      const interval = this.intervals.get(taskId)
      if (interval) {
        clearInterval(interval)
        this.intervals.delete(taskId)
      }
    }
    
    logger.info(`${isActive ? '启用' : '禁用'}任务: ${task.name}`)
  }

  /**
   * 启动调度器
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('调度器已在运行')
      return
    }

    this.isRunning = true
    logger.info('启动任务调度器')

    // 为所有活跃任务设置定时器
    for (const task of this.tasks.values()) {
      if (task.isActive) {
        this.scheduleTask(task)
      }
    }
  }

  /**
   * 停止调度器
   */
  stop(): void {
    if (!this.isRunning) {
      logger.warn('调度器未在运行')
      return
    }

    this.isRunning = false
    logger.info('停止任务调度器')

    // 清除所有定时器
    for (const interval of this.intervals.values()) {
      clearInterval(interval)
    }
    this.intervals.clear()
  }

  /**
   * 手动执行任务
   */
  async executeTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`)
    }

    logger.info(`手动执行任务: ${task.name}`)
    
    try {
      await task.handler()
      task.lastRun = new Date()
      task.nextRun = this.calculateNextRun(task.cronExpression)
      logger.info(`任务执行成功: ${task.name}`)
    } catch (error) {
      logger.error(`任务执行失败: ${task.name}`, error)
      throw error
    }
  }

  /**
   * 获取所有任务状态
   */
  getTasks(): ScheduledTask[] {
    return Array.from(this.tasks.values())
  }

  /**
   * 获取调度器状态
   */
  getStatus(): {
    isRunning: boolean
    taskCount: number
    activeTasks: number
    nextExecution?: { taskId: string; taskName: string; nextRun: Date }
  } {
    const activeTasks = Array.from(this.tasks.values()).filter(t => t.isActive)
    const nextTask = activeTasks
      .filter(t => t.nextRun)
      .sort((a, b) => a.nextRun!.getTime() - b.nextRun!.getTime())[0]

    return {
      isRunning: this.isRunning,
      taskCount: this.tasks.size,
      activeTasks: activeTasks.length,
      nextExecution: nextTask ? {
        taskId: nextTask.id,
        taskName: nextTask.name,
        nextRun: nextTask.nextRun!
      } : undefined
    }
  }

  /**
   * 调度单个任务
   */
  private scheduleTask(task: ScheduledTask): void {
    // 清除现有的定时器
    const existingInterval = this.intervals.get(task.id)
    if (existingInterval) {
      clearInterval(existingInterval)
    }

    // 计算下次执行时间
    const nextRun = task.nextRun || this.calculateNextRun(task.cronExpression)
    const delay = nextRun.getTime() - Date.now()

    if (delay <= 0) {
      // 立即执行
      this.executeTaskSafely(task)
      // 重新调度下次执行
      setTimeout(() => this.scheduleTask(task), 1000)
      return
    }

    // 设置定时器
    const timeout = setTimeout(async () => {
      await this.executeTaskSafely(task)
      // 重新调度下次执行
      this.scheduleTask(task)
    }, delay)

    this.intervals.set(task.id, timeout)
    logger.debug(`任务 ${task.name} 已调度，下次执行时间: ${nextRun.toISOString()}`)
  }

  /**
   * 安全执行任务（带错误处理）
   */
  private async executeTaskSafely(task: ScheduledTask): Promise<void> {
    try {
      logger.info(`执行定时任务: ${task.name}`)
      await task.handler()
      task.lastRun = new Date()
      task.nextRun = this.calculateNextRun(task.cronExpression)
      logger.info(`定时任务执行成功: ${task.name}`)
    } catch (error) {
      logger.error(`定时任务执行失败: ${task.name}`, error)
      // 任务失败不影响调度器继续运行
    }
  }

  /**
   * 计算下次执行时间（简化版cron解析）
   */
  private calculateNextRun(cronExpression: string): Date {
    // 简化的cron解析，支持基本格式
    // 格式: 分钟 小时 日 月 星期
    const parts = cronExpression.split(' ')
    if (parts.length !== 5) {
      throw new Error(`无效的cron表达式: ${cronExpression}`)
    }

    const now = new Date()
    const next = new Date(now)

    // 解析分钟
    const minute = parts[0]
    if (minute.startsWith('*/')) {
      const interval = parseInt(minute.substring(2))
      const currentMinute = now.getMinutes()
      const nextMinute = Math.ceil((currentMinute + 1) / interval) * interval
      next.setMinutes(nextMinute, 0, 0)
      if (nextMinute >= 60) {
        next.setHours(next.getHours() + 1)
        next.setMinutes(nextMinute - 60)
      }
    } else if (minute !== '*') {
      const targetMinute = parseInt(minute)
      next.setMinutes(targetMinute, 0, 0)
      if (targetMinute <= now.getMinutes()) {
        next.setHours(next.getHours() + 1)
      }
    }

    // 解析小时
    const hour = parts[1]
    if (hour !== '*') {
      const targetHour = parseInt(hour)
      next.setHours(targetHour)
      if (targetHour <= now.getHours() && next <= now) {
        next.setDate(next.getDate() + 1)
      }
    }

    // 确保下次执行时间在未来
    if (next <= now) {
      next.setTime(now.getTime() + 60000) // 至少1分钟后
    }

    return next
  }

  /**
   * 新闻拉取任务处理器
   */
  private async handleNewsFetch(): Promise<void> {
    const result = await newsService.fetchAllNews()
    logger.info(`定时新闻拉取完成 - 总计: ${result.total}, 保存: ${result.saved}, 错误: ${result.errors}`)
  }

  /**
   * 数据清理任务处理器
   */
  private async handleDataCleanup(): Promise<void> {
    const deletedCount = await newsService.cleanupOldNews()
    logger.info(`数据清理完成 - 删除了 ${deletedCount} 条旧新闻`)
  }
}

// 创建单例实例
export const schedulerService = new SchedulerService()
